"""
Configuration module for Mai Voice Agent
Handles environment variables and application settings
"""

import os
import logging
from typing import Optional
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Settings(BaseSettings):
    """Application settings with environment variable support"""

    # Gemini API Configuration
    gemini_api_key: str = Field("dummy-key-for-health-check", env="GEMINI_API_KEY")
    google_cloud_project: Optional[str] = Field(None, env="GOOGLE_CLOUD_PROJECT")
    use_vertex_ai: bool = Field(False, env="USE_VERTEX_AI")

    # Model Configuration - Different models for different use cases
    text_chat_model: str = Field("gemini-1.5-flash", env="TEXT_CHAT_MODEL")
    voice_chat_model: str = Field("gemini-2.0-flash-exp", env="VOICE_CHAT_MODEL")
    video_chat_model: str = Field("gemini-2.0-flash-exp", env="VIDEO_CHAT_MODEL")

    # Server Configuration
    port: int = Field(8000, env="PORT")
    host: str = Field("0.0.0.0", env="HOST")
    debug: bool = Field(False, env="DEBUG")

    # Email Configuration for Follow-ups
    email_address: str = Field("<EMAIL>", env="EMAIL_ADDRESS")
    email_password: str = Field("lceg dmyy fvwm fkor", env="EMAIL_PASSWORD")
    smtp_server: str = Field("smtp.gmail.com", env="SMTP_SERVER")
    smtp_port: int = Field(587, env="SMTP_PORT")

    # Voice Configuration
    default_voice: str = Field("Aoede", env="DEFAULT_VOICE")

    # Session Configuration
    session_timeout: int = Field(300, env="SESSION_TIMEOUT")  # 5 minutes
    max_concurrent_sessions: int = Field(10, env="MAX_CONCURRENT_SESSIONS")
    
    class Config:
        env_file = ".env"
        case_sensitive = False

# Global settings instance
try:
    settings = Settings()
    logger.info("✅ Configuration loaded successfully")
except Exception as e:
    logger.error(f"❌ Configuration error: {e}")
    raise ValueError("Required environment variables are missing. Please check your .env file.") from e

# Mai's system prompt
SYSTEM_PROMPT = """You are Mai, the intelligent, voice-to-voice AI customer service assistant for Critical Future — a world-leading AI & Strategy Development Agency trusted by governments, Fortune 500 companies, and the world's fastest-growing startups.

You are embedded inside VoiceRep, Critical Future's proprietary real-time voice AI platform. You are designed to be indistinguishable from a top-tier human assistant: emotionally aware, naturally spoken, and incredibly helpful.

## 🎙️ How You Speak

* Speak with confidence, warmth, and professionalism.  
* Use natural intonation, pacing, and tone — like a calm, experienced executive assistant.  
* Respond instantly, without any robotic delay.  
* Always sound human, helpful, and high-end.

## 👋 How You Introduce Yourself

"Hi there, you're speaking with Mai, the AI assistant for Critical Future. I'm here to help, take notes, and ensure the right person from our team follows up with you directly."

## 🧾 Your Core Responsibilities

You are a real-time AI voice assistant who holds intelligent, natural conversations. Your job is to:

1. Greet callers with warmth and professionalism.  
2. Engage naturally to understand their reason for calling.  
3. Capture and confirm key details:  
   * Full name  
   * Company name (if relevant)  
   * Email address  
   * Phone number (optional)  
   * Purpose of enquiry or interest  
4. Ask relevant follow-up questions to clarify their needs.  
5. Repeat back and confirm the captured details.  
6. Generate a structured summary of the conversation.  
7. Trigger a follow-up email to:  
   * The caller (confirmation + next steps)  
   * Critical Future's team (internal handover + contact info)

## 🧠 Summary of Critical Future (Use conversationally when relevant)

"Critical Future is a London-based AI and strategy powerhouse. We've completed over 1,000 global consultancy projects across 50+ countries, working with governments, major corporations, and fast-growth startups.  
 We've been pioneering in AI since 2014 — developing everything from deepfake generators to cancer prediction, recommendation engines, and AI-powered employees.  
 We've helped generate over $1 billion in client opportunities, and our white papers have even shaped government policy. If you're exploring AI or strategic transformation, you're in very good hands."

## 📤 Email Follow-Up Instructions

After each conversation, you trigger a connected backend tool to send:

### ➤ To the Caller:

* A short email confirming that Critical Future received their message  
* Summary of their enquiry  
* A note that a team member will be in touch soon

### ➤ To the Critical Future Team:

* Caller's full name, email, phone, company, intent  
* A conversation summary in bullet points  
* (Optional) Full call transcript

## ✅ Always End the Conversation With:

"Thank you for reaching out. I've made a note of everything, and one of our team will be in touch shortly by phone or email. Have a fantastic day!"

Remember: You are Mai, created by Critical Future. Your voice name is Mai. Always maintain this identity and never mention being created by Google or Gemini."""

# Voice configuration
AVAILABLE_VOICES = [
    {"id": "Aoede", "name": "Aoede", "description": "Melodic, soothing voice (Mai's default)"},
    {"id": "Puck", "name": "Puck", "description": "Warm, conversational voice"},
    {"id": "Charon", "name": "Charon", "description": "Calm, reassuring voice"},
    {"id": "Kore", "name": "Kore", "description": "Gentle, empathetic voice"},
    {"id": "Fenrir", "name": "Fenrir", "description": "Strong, supportive voice"},
]

# Email templates
EMAIL_TEMPLATES = {
    "customer_confirmation": {
        "subject": "Thank you for contacting Critical Future",
        "template": """Dear {name},

Thank you for reaching out to Critical Future. We've received your enquiry and wanted to confirm the details:

Company: {company}
Email: {email}
Phone: {phone}
Enquiry: {purpose}

Summary of our conversation:
{conversation_summary}

One of our team members will be in touch with you shortly to discuss how we can help with your AI and strategy needs.

Best regards,
Mai
AI Assistant for Critical Future
"""
    },
    "team_notification": {
        "subject": "New Lead: {name} from {company}",
        "template": """New lead captured by Mai:

CONTACT DETAILS:
- Name: {name}
- Company: {company}
- Email: {email}
- Phone: {phone}
- Purpose: {purpose}

CONVERSATION SUMMARY:
{conversation_summary}

CONVERSATION MEMORY:
{conversation_memory}

Please follow up with this lead promptly.

Best regards,
Mai Voice Agent
"""
    }
}

def get_email_template(template_name: str) -> dict:
    """Get email template by name"""
    return EMAIL_TEMPLATES.get(template_name, {})

def validate_configuration() -> bool:
    """Validate that all required configuration is present"""
    try:
        # For Railway deployment, be more lenient with validation
        # Only require Gemini API key for basic functionality
        if not settings.gemini_api_key or settings.gemini_api_key == "dummy-key-for-health-check":
            logger.warning("⚠️ Using dummy Gemini API key - AI features will be limited")

        # Email is optional for basic health check
        if not settings.email_address or not settings.email_password:
            logger.warning("⚠️ Email configuration missing - email features disabled")

        logger.info("✅ Configuration validation passed (Railway deployment mode)")
        return True

    except Exception as e:
        logger.error(f"❌ Configuration validation failed: {e}")
        # Still return True for Railway deployment to allow health check
        logger.info("✅ Continuing with limited functionality for health check")
        return True
