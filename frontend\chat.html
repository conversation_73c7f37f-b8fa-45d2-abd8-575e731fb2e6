<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>Mai Voice Agent - Chat</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 90vh;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .chat-header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .chat-header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .status-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4CAF50;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .message {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.user {
            align-self: flex-end;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .message.ai {
            align-self: flex-start;
            background: #f1f3f4;
            color: #333;
            border: 1px solid #e0e0e0;
        }

        .message.system {
            align-self: center;
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            font-size: 14px;
            text-align: center;
        }

        .message-time {
            font-size: 11px;
            opacity: 0.7;
            margin-top: 5px;
        }

        .chat-input-container {
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #e0e0e0;
        }

        .chat-input-wrapper {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            resize: none;
            max-height: 120px;
            min-height: 44px;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .chat-input:focus {
            border-color: #667eea;
        }

        .send-button {
            width: 44px;
            height: 44px;
            border: none;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.2s ease;
        }

        .send-button:hover {
            transform: scale(1.05);
        }

        .send-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .typing-indicator {
            display: none;
            align-self: flex-start;
            padding: 12px 16px;
            background: #f1f3f4;
            border-radius: 18px;
            border: 1px solid #e0e0e0;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #999;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% {
                transform: scale(0);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 8px;
            margin: 10px 20px;
            text-align: center;
            font-size: 14px;
        }

        @media (max-width: 768px) {
            html, body {
                height: 100%;
                overflow-x: hidden;
            }

            .chat-container {
                width: 100%;
                height: 100vh;
                height: 100dvh; /* Dynamic viewport height for mobile */
                border-radius: 0;
                display: flex;
                flex-direction: column;
                overflow: hidden;
            }

            .chat-header {
                flex-shrink: 0;
                padding: 15px;
            }

            .chat-header h1 {
                font-size: 20px;
                margin-bottom: 3px;
            }

            .chat-header p {
                font-size: 12px;
            }

            .chat-messages {
                flex: 1;
                min-height: 0; /* Critical for proper flex behavior */
                padding: 15px;
                overflow-y: auto;
                overflow-x: hidden;
                -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
            }

            .message {
                max-width: 85%;
                padding: 10px 14px;
                margin-bottom: 12px;
                word-wrap: break-word;
                overflow-wrap: break-word;
            }

            .typing-indicator {
                flex-shrink: 0;
                padding: 10px 20px;
            }

            .chat-input-container {
                flex-shrink: 0;
                padding: 15px;
                background: #f8f9fa;
                border-top: 1px solid #e0e0e0;
            }

            .chat-input-wrapper {
                display: flex;
                gap: 8px;
                align-items: flex-end;
            }

            .chat-input {
                flex: 1;
                padding: 12px 16px;
                font-size: 16px; /* Prevent zoom on iOS */
                min-height: 44px; /* Touch-friendly minimum */
                max-height: 100px;
                border-radius: 22px;
            }

            .send-button {
                width: 44px; /* Touch-friendly size */
                height: 44px;
                flex-shrink: 0;
            }

            /* Ensure proper safe area handling */
            @supports (padding: max(0px)) {
                .chat-container {
                    padding-left: max(0px, env(safe-area-inset-left));
                    padding-right: max(0px, env(safe-area-inset-right));
                    padding-bottom: max(0px, env(safe-area-inset-bottom));
                }
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <div class="status-indicator" id="statusIndicator"></div>
            <h1>Mai Voice Agent</h1>
            <p>AI Assistant for Critical Future</p>
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <div class="message system">
                Hi there! I'm Mai, your AI assistant. How can I help you today?
            </div>
        </div>
        
        <div class="typing-indicator" id="typingIndicator">
            <div class="typing-dots">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
        </div>
        
        <div class="chat-input-container">
            <div class="chat-input-wrapper">
                <textarea 
                    id="chatInput" 
                    class="chat-input" 
                    placeholder="Type your message here..."
                    rows="1"
                ></textarea>
                <button id="sendButton" title="send" class="send-button">
                    <title>Send</title>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"></path>
                    </svg>
                </button>
button      </div>
        </div>
    </div>

    <script src="chat.js"></script>
</body>
</html>
