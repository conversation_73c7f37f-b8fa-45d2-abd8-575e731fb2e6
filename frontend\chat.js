class ChatApp {
    constructor() {
        this.apiBaseUrl = this.detectApiUrl();
        this.sessionId = this.generateSessionId();
        this.isConnected = false;
        this.messageHistory = [];
        
        this.initializeElements();
        this.setupEventListeners();
        this.checkConnection();
        
        console.log('🚀 Chat App initialized');
        console.log('📡 API URL:', this.apiBaseUrl);
        console.log('🔑 Session ID:', this.sessionId);
    }

    detectApiUrl() {
        const hostname = window.location.hostname;
        const port = window.location.port;
        const protocol = window.location.protocol;
        
        // For local development
        if (hostname === 'localhost' || hostname === '127.0.0.1') {
            return `${protocol}//${hostname}:${port || '8000'}/api`;
        }
        
        // For production (Railway, etc.)
        return `${protocol}//${hostname}${port ? ':' + port : ''}/api`;
    }

    generateSessionId() {
        return 'chat_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    initializeElements() {
        this.chatMessages = document.getElementById('chatMessages');
        this.chatInput = document.getElementById('chatInput');
        this.sendButton = document.getElementById('sendButton');
        this.typingIndicator = document.getElementById('typingIndicator');
        this.statusIndicator = document.getElementById('statusIndicator');
    }

    setupEventListeners() {
        // Send button click
        this.sendButton.addEventListener('click', () => this.sendMessage());
        
        // Enter key to send (Shift+Enter for new line)
        this.chatInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // Auto-resize textarea
        this.chatInput.addEventListener('input', () => {
            this.chatInput.style.height = 'auto';
            this.chatInput.style.height = Math.min(this.chatInput.scrollHeight, 120) + 'px';
        });

        // Handle mobile keyboard visibility
        if (/iPhone|iPad|iPod|Android/i.test(navigator.userAgent)) {
            this.chatInput.addEventListener('focus', () => {
                // Small delay to ensure keyboard is shown
                setTimeout(() => {
                    this.chatInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }, 300);
            });

            this.chatInput.addEventListener('blur', () => {
                // Reset viewport when keyboard hides
                setTimeout(() => {
                    window.scrollTo(0, 0);
                }, 100);
            });
        }

        // Focus input on load
        this.chatInput.focus();
    }

    async checkConnection() {
        try {
            console.log('🔍 Checking connection to:', `${this.apiBaseUrl}/health`);
            
            const response = await fetch(`${this.apiBaseUrl}/health`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            if (response.ok) {
                this.setConnectionStatus(true);
                console.log('✅ Connection successful');
            } else {
                throw new Error(`HTTP ${response.status}`);
            }
        } catch (error) {
            console.error('❌ Connection failed:', error);
            this.setConnectionStatus(false);
            this.showError('Unable to connect to the server. Please check your connection.');
        }
    }

    setConnectionStatus(connected) {
        this.isConnected = connected;
        this.statusIndicator.style.background = connected ? '#4CAF50' : '#f44336';
        this.sendButton.disabled = !connected;
    }

    async sendMessage() {
        const message = this.chatInput.value.trim();
        if (!message || !this.isConnected) return;

        // Clear input and disable send button
        this.chatInput.value = '';
        this.chatInput.style.height = 'auto';
        this.sendButton.disabled = true;

        // Add user message to chat
        this.addMessage('user', message);

        // Show typing indicator
        this.showTypingIndicator();

        try {
            console.log('📤 Sending message:', message);
            
            const response = await fetch(`${this.apiBaseUrl}/chat`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    prompt: message,
                    session_id: this.sessionId
                }),
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            console.log('📥 Received response:', data);

            // Hide typing indicator and add AI response
            this.hideTypingIndicator();
            this.addMessage('ai', data.response || data.message || 'Sorry, I didn\'t understand that.');

        } catch (error) {
            console.error('❌ Send message error:', error);
            this.hideTypingIndicator();
            
            let errorMessage = 'Sorry, I encountered an error. Please try again.';
            if (error.message.includes('405')) {
                errorMessage = 'Chat service is temporarily unavailable. Please try again later.';
            } else if (error.message.includes('500')) {
                errorMessage = 'Server error occurred. Please try again in a moment.';
            }
            
            this.addMessage('system', errorMessage);
        } finally {
            // Re-enable send button
            this.sendButton.disabled = false;
            this.chatInput.focus();
        }
    }

    addMessage(type, content) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;
        
        const contentDiv = document.createElement('div');
        contentDiv.textContent = content;
        messageDiv.appendChild(contentDiv);
        
        const timeDiv = document.createElement('div');
        timeDiv.className = 'message-time';
        timeDiv.textContent = new Date().toLocaleTimeString();
        messageDiv.appendChild(timeDiv);
        
        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
        
        // Store in history
        this.messageHistory.push({ type, content, timestamp: new Date() });
    }

    showTypingIndicator() {
        this.typingIndicator.style.display = 'flex';
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        this.typingIndicator.style.display = 'none';
    }

    scrollToBottom() {
        setTimeout(() => {
            this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
        }, 100);
    }

    showError(message) {
        // Remove existing error messages
        const existingErrors = document.querySelectorAll('.error-message');
        existingErrors.forEach(error => error.remove());
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.textContent = message;
        
        this.chatMessages.parentNode.insertBefore(errorDiv, this.chatMessages.nextSibling);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.remove();
            }
        }, 5000);
    }
}

// Initialize the chat app when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.chatApp = new ChatApp();
});

// Handle page visibility changes to reconnect if needed
document.addEventListener('visibilitychange', () => {
    if (!document.hidden && window.chatApp) {
        window.chatApp.checkConnection();
    }
});
